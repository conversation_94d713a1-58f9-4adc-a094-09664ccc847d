import './App.css'
import Navbar from './components/Navbar'
import Home from './pages/Home'
import Products from './pages/Products'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import { useScrollAnimation } from './hooks/useScrollAnimation'
import { AppProvider } from './context/AppContext'
import { ToastProvider } from './context/ToastContext'

function App() {
  useScrollAnimation()

  return (
    <ToastProvider>
      <AppProvider>
        <BrowserRouter>
          <Navbar/>
          <Routes>
            <Route path='/' element={<Home/>}/>
            <Route path='/products' element={<Products/>}/>
          </Routes>
        </BrowserRouter>
      </AppProvider>
    </ToastProvider>
  )
}

export default App
