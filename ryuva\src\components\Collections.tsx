
import React, { useState, useEffect } from 'react'
import { apiService, Product } from '../services/api'
import ProductCard from './ProductCard'

const Collections: React.FC = () => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadFeaturedProducts()
  }, [])

  const loadFeaturedProducts = async () => {
    try {
      setLoading(true)
      const response = await apiService.getFeaturedProducts()
      setFeaturedProducts(response.data.products)
    } catch (err: any) {
      setError(err.message || 'Failed to load products')
    } finally {
      setLoading(false)
    }
  }

  const categories = [
    {
      id: 'heritage',
      name: 'Heritage Rumaals',
      description: 'Timeless designs celebrating rich cultural patterns and artisanal craftsmanship.',
      image: 'https://placehold.co/400x300/B08D57/FFFFFF?text=Heritage+Rumaal&font=playfairdisplay'
    },
    {
      id: 'minimal-luxe',
      name: 'Minimal Luxe',
      description: 'Clean lines, subtle textures, and sophisticated simplicity for the modern minimalist.',
      image: 'https://placehold.co/400x300/36454F/FFFFFF?text=Minimal+Luxe&font=playfairdisplay'
    },
    {
      id: 'wedding-gifting',
      name: 'Wedding Gifting',
      description: 'Elegant rumaals perfect for memorable wedding favors and bridal party gifts.',
      image: 'https://placehold.co/400x300/2E4034/FFFFFF?text=Wedding+Gifting&font=playfairdisplay'
    },
    {
      id: 'custom-monogram',
      name: 'Custom Monogram',
      description: 'Personalize your rumaal with bespoke monograms and unique designs.',
      image: 'https://placehold.co/400x300/D1C0A8/333333?text=Custom+Monogram&font=playfairdisplay'
    }
  ]

  return (
    <section id="collections" className="py-16 md:py-24 bg-ryuva-cream scroll-animate">
      <div className="container mx-auto px-6">
        <h2 className="text-4xl font-serif font-bold text-ryuva-deep-green text-center mb-16">
          Discover Our Collections
        </h2>

        {/* Category Overview */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {categories.map((category) => (
            <div key={category.id} className="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
              <img
                src={category.image}
                alt={category.name}
                className="w-full h-56 object-cover"
              />
              <div className="p-6 flex flex-col flex-grow">
                <h3 className="text-2xl font-serif font-semibold text-ryuva-deep-green mb-2">
                  {category.name}
                </h3>
                <p className="text-ryuva-charcoal text-sm mb-4 flex-grow">
                  {category.description}
                </p>
                <a
                  href={`/collections/${category.id}`}
                  className="mt-auto self-start text-ryuva-gold font-semibold hover:underline"
                >
                  Explore {category.name} <span aria-hidden="true">&rarr;</span>
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Featured Products */}
        <div className="mb-8">
          <h3 className="text-3xl font-serif font-bold text-ryuva-deep-green text-center mb-12">
            Featured Products
          </h3>

          {loading && (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-ryuva-gold"></div>
              <p className="mt-4 text-ryuva-charcoal">Loading featured products...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={loadFeaturedProducts}
                className="bg-ryuva-gold text-white px-6 py-2 rounded-lg hover:bg-opacity-90 transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {!loading && !error && featuredProducts.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}

          {!loading && !error && featuredProducts.length === 0 && (
            <div className="text-center py-12">
              <p className="text-ryuva-charcoal">No featured products available at the moment.</p>
            </div>
          )}
        </div>

        {/* View All Products Link */}
        <div className="text-center">
          <a
            href="/products"
            className="inline-block bg-ryuva-deep-green text-white py-3 px-8 rounded-lg text-lg font-semibold hover:bg-opacity-90 transition-all duration-300 transform hover:scale-105"
          >
            View All Products
          </a>
        </div>
      </div>
    </section>
  )
}

export default Collections