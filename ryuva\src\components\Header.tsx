import React, { useState } from 'react'
import { useApp } from '../context/AppContext'
import LoginModal from './LoginModal'

const Header: React.FC = () => {
  const { state, actions } = useApp()
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [isLoginModalOpen, setIsLoginModalOpen] = useState<boolean>(false)

  const handleIsOpen = () => {
    setIsOpen(!isOpen)
  }

  const handleLoginClick = () => {
    setIsLoginModalOpen(true)
  }

  const handleLogout = () => {
    actions.logout()
  }

  return (
    <>
      <nav className="bg-white/90 backdrop-blur-lg shadow-md fixed w-full z-50 top-0 transition-all duration-300">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex-shrink-0">
              <a href="#home" className="text-4xl font-serif font-bold text-ryuva-deep-green hover:text-ryuva-gold transition-colors">
                Ryuva
              </a>
            </div>

            <div className="hidden md:flex items-center space-x-6">
              <a href="/" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
                Home
              </a>
              <a href="/products" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
                Products
              </a>
              <a href="#collections" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
                Collections
              </a>
              <a href="#about" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
                About
              </a>
              <a href="#contact" className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium">
                Contact
              </a>

              {/* Cart Icon */}
              <div className="relative">
                <button className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                  </svg>
                  {state.cart && state.cart.totalItems > 0 && (
                    <span className="absolute -top-1 -right-1 bg-ryuva-gold text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {state.cart.totalItems}
                    </span>
                  )}
                </button>
              </div>

              {/* Wishlist Icon */}
              <div className="relative">
                <button className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                  </svg>
                  {state.wishlist.length > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {state.wishlist.length}
                    </span>
                  )}
                </button>
              </div>

              {/* User Authentication */}
              {state.isAuthenticated ? (
                <div className="flex items-center space-x-4">
                  <span className="text-ryuva-charcoal font-medium">
                    Hello, {state.user?.firstName}
                  </span>
                  <button
                    onClick={handleLogout}
                    className="text-ryuva-charcoal hover:text-ryuva-gold transition-colors font-medium"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <button
                  onClick={handleLoginClick}
                  className="ml-4 bg-ryuva-gold text-white py-2.5 px-6 rounded-lg hover:bg-opacity-80 transition-all duration-300 ease-in-out shadow-sm font-semibold text-sm"
                >
                  Sign In
                </button>
              )}
            </div>

          <div className="md:hidden">
            <button
              id="mobile-menu-button"
              type="button"
              onClick={handleIsOpen}
              className="inline-flex items-center justify-center p-2 rounded-md text-ryuva-charcoal hover:text-ryuva-gold hover:bg-ryuva-light-gray focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ryuva-gold"
              aria-controls="mobile-menu"
              aria-expanded={isOpen}
            >
              <span className="sr-only">Open main menu</span>
              {!isOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="md:hidden" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <a href="#about" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              About
            </a>
            <a href="#collections" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              Collections
            </a>
            <a href="#why-ryuva" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              Why Ryuva?
            </a>
            <a href="#contact" className="text-ryuva-charcoal hover:bg-ryuva-light-gray hover:text-ryuva-gold block px-3 py-2 rounded-md text-base font-medium">
              Contact
            </a>
            <a href="#shop" className="bg-ryuva-gold text-white block w-full text-center mt-2 py-2.5 px-6 rounded-lg hover:bg-opacity-80 transition-all duration-300 ease-in-out shadow-sm font-semibold text-sm">
              Shop Now
            </a>
          </div>
        </div>
      )}
      </nav>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  )
}

export default Header