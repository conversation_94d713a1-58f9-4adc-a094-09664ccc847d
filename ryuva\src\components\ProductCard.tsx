import React, { useState } from 'react';
import { Product } from '../services/api';
import { useApp } from '../context/AppContext';
import { useToast } from '../context/ToastContext';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { state, actions } = useApp();
  const { showToast } = useToast();
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isAddingToWishlist, setIsAddingToWishlist] = useState(false);

  const isInWishlist = state.wishlist.some(item => item.id === product.id);

  const handleAddToCart = async () => {
    if (!state.isAuthenticated) {
      showToast('Please login to add items to cart', 'info');
      return;
    }

    try {
      setIsAddingToCart(true);
      await actions.addToCart(product.id, 1);
      showToast(`${product.name} added to cart!`, 'success');
    } catch (error: any) {
      console.error('Failed to add to cart:', error);
      showToast(error.message || 'Failed to add item to cart', 'error');
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleWishlistToggle = async () => {
    if (!state.isAuthenticated) {
      showToast('Please login to manage wishlist', 'info');
      return;
    }

    try {
      setIsAddingToWishlist(true);
      if (isInWishlist) {
        await actions.removeFromWishlist(product.id);
        showToast(`${product.name} removed from wishlist`, 'success');
      } else {
        await actions.addToWishlist(product.id);
        showToast(`${product.name} added to wishlist!`, 'success');
      }
    } catch (error: any) {
      console.error('Failed to update wishlist:', error);
      showToast(error.message || 'Failed to update wishlist', 'error');
    } finally {
      setIsAddingToWishlist(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-xl overflow-hidden group transform hover:scale-105 transition-transform duration-300 flex flex-col">
      <div className="relative">
        <img 
          src={product.images[0]?.url} 
          alt={product.images[0]?.alt || product.name}
          className="w-full h-56 object-cover"
        />
        
        {/* Discount Badge */}
        {product.discountPercentage > 0 && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-sm font-semibold">
            {product.discountPercentage}% OFF
          </div>
        )}
        
        {/* Wishlist Button */}
        <button
          onClick={handleWishlistToggle}
          disabled={isAddingToWishlist}
          className={`absolute top-2 right-2 p-2 rounded-full transition-colors ${
            isInWishlist 
              ? 'bg-red-500 text-white' 
              : 'bg-white text-gray-600 hover:bg-red-50 hover:text-red-500'
          } ${isAddingToWishlist ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill={isInWishlist ? "currentColor" : "none"} viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5">
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
          </svg>
        </button>

        {/* Stock Status */}
        {product.inventory.stockStatus === 'low-stock' && (
          <div className="absolute bottom-2 left-2 bg-orange-500 text-white px-2 py-1 rounded-md text-xs">
            Low Stock
          </div>
        )}
        {product.inventory.stockStatus === 'out-of-stock' && (
          <div className="absolute bottom-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs">
            Out of Stock
          </div>
        )}
      </div>
      
      <div className="p-6 flex flex-col flex-grow">
        <div className="mb-2">
          <span className="text-xs text-ryuva-gold font-medium uppercase tracking-wider">
            {product.category.replace('-', ' ')}
          </span>
        </div>
        
        <h3 className="text-xl font-serif font-semibold text-ryuva-deep-green mb-2 line-clamp-2">
          {product.name}
        </h3>
        
        <p className="text-ryuva-charcoal text-sm mb-4 flex-grow line-clamp-3">
          {product.shortDescription}
        </p>

        {/* Features */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {product.features.slice(0, 2).map((feature, index) => (
              <span key={index} className="text-xs bg-ryuva-cream text-ryuva-deep-green px-2 py-1 rounded">
                {feature}
              </span>
            ))}
          </div>
        </div>

        {/* Fabric Info */}
        <div className="mb-4 text-sm text-gray-600">
          <span className="font-medium">Fabric:</span> {product.fabric.type}
          {product.fabric.origin && (
            <span className="ml-2">• {product.fabric.origin}</span>
          )}
        </div>

        {/* Ratings */}
        <div className="flex items-center mb-4">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.ratings.average) 
                    ? 'text-yellow-400' 
                    : 'text-gray-300'
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
            <span className="ml-2 text-sm text-gray-600">
              {product.ratings.average} ({product.ratings.count})
            </span>
          </div>
        </div>

        {/* Pricing */}
        <div className="mb-4">
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold text-ryuva-deep-green">
              ₹{product.price.toLocaleString()}
            </span>
            {product.comparePrice && (
              <span className="text-lg text-gray-500 line-through">
                ₹{product.comparePrice.toLocaleString()}
              </span>
            )}
          </div>
          {product.isCustomizable && (
            <p className="text-sm text-ryuva-gold mt-1">+ Customization available</p>
          )}
        </div>

        {/* Actions */}
        <div className="mt-auto space-y-2">
          <button
            onClick={handleAddToCart}
            disabled={isAddingToCart || product.inventory.stockStatus === 'out-of-stock'}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 ${
              product.inventory.stockStatus === 'out-of-stock'
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : isAddingToCart
                ? 'bg-ryuva-gold/70 text-white cursor-not-allowed'
                : 'bg-ryuva-gold text-white hover:bg-opacity-90 transform hover:scale-105'
            }`}
          >
            {isAddingToCart ? 'Adding...' : 
             product.inventory.stockStatus === 'out-of-stock' ? 'Out of Stock' : 
             'Add to Cart'}
          </button>
          
          <a 
            href={`/products/${product.id}`}
            className="block w-full text-center py-2 px-4 border-2 border-ryuva-gold text-ryuva-gold rounded-lg font-semibold hover:bg-ryuva-gold hover:text-white transition-all duration-300"
          >
            View Details
          </a>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
