import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { apiService, User, Cart, Product } from '../services/api';

interface AppState {
  user: User | null;
  cart: Cart | null;
  wishlist: Product[];
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

interface AppActions {
  login: (email: string, password: string) => Promise<any>;
  logout: () => void;
  loadUserData: () => Promise<void>;
  addToCart: (productId: number, quantity?: number, customization?: any) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  addToWishlist: (productId: number) => Promise<void>;
  removeFromWishlist: (productId: number) => Promise<void>;
  clearError: () => void;
}

const initialState: AppState = {
  user: null,
  cart: null,
  wishlist: [],
  isAuthenticated: false,
  loading: true,
  error: null,
};

type AppAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_CART'; payload: Cart | null }
  | { type: 'SET_WISHLIST'; payload: Product[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' };

const AppContext = createContext<{
  state: AppState;
  actions: AppActions;
}>({
  state: initialState,
  actions: {} as AppActions,
});

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return { 
        ...state, 
        user: action.payload, 
        isAuthenticated: !!action.payload 
      };
    case 'SET_CART':
      return { ...state, cart: action.payload };
    case 'SET_WISHLIST':
      return { ...state, wishlist: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const actions: AppActions = {
    async login(email: string, password: string) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        dispatch({ type: 'SET_LOADING', payload: true });
        
        const response = await apiService.login(email, password);
        dispatch({ type: 'SET_USER', payload: response.data.user });
        
        // Load user data after login
        await actions.loadUserData();
        
        return response;
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    logout() {
      apiService.logout();
      dispatch({ type: 'SET_USER', payload: null });
      dispatch({ type: 'SET_CART', payload: null });
      dispatch({ type: 'SET_WISHLIST', payload: [] });
    },

    async loadUserData() {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        const token = localStorage.getItem('authToken');
        if (!token) {
          dispatch({ type: 'SET_LOADING', payload: false });
          return;
        }

        const [userResponse, cartResponse, wishlistResponse] = await Promise.allSettled([
          apiService.getCurrentUser(),
          apiService.getCart(),
          apiService.getWishlist(),
        ]);
        
        if (userResponse.status === 'fulfilled') {
          dispatch({ type: 'SET_USER', payload: userResponse.value.data.user });
        }
        
        if (cartResponse.status === 'fulfilled') {
          dispatch({ type: 'SET_CART', payload: cartResponse.value.data.cart });
        }
        
        if (wishlistResponse.status === 'fulfilled') {
          dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.value.data.wishlist });
        }
      } catch (error: any) {
        console.error('Failed to load user data:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    async addToCart(productId: number, quantity: number = 1, customization?: any) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.addToCart(productId, quantity, customization);
        
        // Reload cart data
        const cartResponse = await apiService.getCart();
        dispatch({ type: 'SET_CART', payload: cartResponse.data.cart });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async removeFromCart(itemId: number) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.removeFromCart(itemId);
        
        // Reload cart data
        const cartResponse = await apiService.getCart();
        dispatch({ type: 'SET_CART', payload: cartResponse.data.cart });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async addToWishlist(productId: number) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.addToWishlist(productId);
        
        // Reload wishlist data
        const wishlistResponse = await apiService.getWishlist();
        dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.data.wishlist });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    async removeFromWishlist(productId: number) {
      try {
        dispatch({ type: 'SET_ERROR', payload: null });
        await apiService.removeFromWishlist(productId);
        
        // Reload wishlist data
        const wishlistResponse = await apiService.getWishlist();
        dispatch({ type: 'SET_WISHLIST', payload: wishlistResponse.data.wishlist });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message });
        throw error;
      }
    },

    clearError() {
      dispatch({ type: 'CLEAR_ERROR' });
    },
  };

  useEffect(() => {
    const token = localStorage.getItem('authToken');
    if (token) {
      actions.loadUserData();
    } else {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  return (
    <AppContext.Provider value={{ state, actions }}>
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
