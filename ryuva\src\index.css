@import "tailwindcss";

@theme {
  --font-sans: "Inter", sans-serif;
  --font-serif: "Playfair Display", serif;

  --color-ryuva-gold: #B08D57;
  --color-ryuva-deep-green: #2E4034;
  --color-ryuva-cream: #FDFBF5;
  --color-ryuva-charcoal: #36454F;
  --color-ryuva-light-gray: #E5E7EB;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Body styling */
body {
  font-family: 'Inter', sans-serif;
  background-color: var(--color-ryuva-cream);
  color: var(--color-ryuva-charcoal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Simple fade-in animation for sections on scroll */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-animate.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Shadow text for hero section */
.shadow-text {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Testimonial slider navigation styling */
.testimonial-slider-nav button {
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  transition: background-color 0.3s;
}

.testimonial-slider-nav button:hover {
  background-color: rgba(0, 0, 0, 0.5);
}