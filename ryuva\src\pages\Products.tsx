import React, { useState, useEffect } from 'react';
import { apiService, Product } from '../services/api';
import ProductCard from '../components/ProductCard';

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    category: '',
    search: '',
    page: 1,
    limit: 12
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0
  });

  useEffect(() => {
    loadProducts();
  }, [filters]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiService.getProducts({
        page: filters.page,
        limit: filters.limit,
        ...(filters.category && { category: filters.category }),
        ...(filters.search && { search: filters.search })
      });
      
      setProducts(response.data.products);
      setPagination(response.data.pagination);
    } catch (err: any) {
      setError(err.message || 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryFilter = (category: string) => {
    setFilters(prev => ({
      ...prev,
      category: category === prev.category ? '' : category,
      page: 1
    }));
  };

  const handleSearch = (search: string) => {
    setFilters(prev => ({
      ...prev,
      search,
      page: 1
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const categories = [
    { id: 'heritage', name: 'Heritage', color: 'bg-amber-100 text-amber-800' },
    { id: 'minimal-luxe', name: 'Minimal Luxe', color: 'bg-gray-100 text-gray-800' },
    { id: 'wedding-gifting', name: 'Wedding Gifting', color: 'bg-pink-100 text-pink-800' },
    { id: 'custom-monogram', name: 'Custom Monogram', color: 'bg-blue-100 text-blue-800' }
  ];

  return (
    <div className="min-h-screen bg-ryuva-cream pt-24 pb-16">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-serif font-bold text-ryuva-deep-green mb-4">
            Our Collection
          </h1>
          <p className="text-xl text-ryuva-charcoal max-w-2xl mx-auto">
            Discover our exquisite range of handcrafted rumaals, from traditional heritage designs to modern minimalist styles.
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="w-full lg:w-96">
              <input
                type="text"
                placeholder="Search products..."
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ryuva-gold focus:border-ryuva-gold transition-colors"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryFilter(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    filters.category === category.id
                      ? 'bg-ryuva-gold text-white'
                      : `${category.color} hover:bg-ryuva-gold hover:text-white`
                  }`}
                >
                  {category.name}
                </button>
              ))}
              {filters.category && (
                <button
                  onClick={() => handleCategoryFilter('')}
                  className="px-4 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors"
                >
                  Clear Filter
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Results Info */}
        {!loading && (
          <div className="mb-6 text-ryuva-charcoal">
            <p>
              Showing {products.length} of {pagination.total} products
              {filters.category && (
                <span className="ml-2 text-ryuva-gold font-medium">
                  in {categories.find(c => c.id === filters.category)?.name}
                </span>
              )}
              {filters.search && (
                <span className="ml-2 text-ryuva-gold font-medium">
                  for "{filters.search}"
                </span>
              )}
            </p>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-16">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-ryuva-gold"></div>
            <p className="mt-4 text-ryuva-charcoal text-lg">Loading products...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-16">
            <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg inline-block">
              <p className="mb-4">{error}</p>
              <button 
                onClick={loadProducts}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* Products Grid */}
        {!loading && !error && products.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
            {products.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}

        {/* No Products Found */}
        {!loading && !error && products.length === 0 && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-serif font-bold text-ryuva-deep-green mb-2">
              No products found
            </h3>
            <p className="text-ryuva-charcoal mb-6">
              Try adjusting your search or filter criteria
            </p>
            <button
              onClick={() => {
                setFilters({ category: '', search: '', page: 1, limit: 12 });
              }}
              className="bg-ryuva-gold text-white px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors"
            >
              View All Products
            </button>
          </div>
        )}

        {/* Pagination */}
        {!loading && !error && pagination.pages > 1 && (
          <div className="flex justify-center items-center space-x-2">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-ryuva-gold hover:text-white hover:border-ryuva-gold transition-colors"
            >
              Previous
            </button>
            
            {[...Array(pagination.pages)].map((_, index) => {
              const page = index + 1;
              return (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-4 py-2 border rounded-lg transition-colors ${
                    pagination.page === page
                      ? 'bg-ryuva-gold text-white border-ryuva-gold'
                      : 'border-gray-300 hover:bg-ryuva-gold hover:text-white hover:border-ryuva-gold'
                  }`}
                >
                  {page}
                </button>
              );
            })}
            
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.pages}
              className="px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-ryuva-gold hover:text-white hover:border-ryuva-gold transition-colors"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Products;
