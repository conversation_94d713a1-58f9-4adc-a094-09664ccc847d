const API_BASE_URL = 'http://localhost:3000/api';

export interface Product {
  id: number;
  name: string;
  description: string;
  shortDescription: string;
  sku: string;
  category: string;
  subcategory: string;
  price: number;
  comparePrice?: number;
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  fabric: {
    type: string;
    care: string[];
    origin: string;
  };
  colors: Array<{
    name: string;
    hex: string;
  }>;
  patterns: string[];
  tags: string[];
  inventory: {
    quantity: number;
    stockStatus: string;
  };
  features: string[];
  isCustomizable: boolean;
  customizationOptions?: {
    monogram?: {
      enabled: boolean;
      price: number;
      positions: string[];
      fonts: string[];
      colors: string[];
    };
  };
  ratings: {
    average: number;
    count: number;
  };
  isFeatured: boolean;
  discountPercentage: number;
}

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
}

export interface CartItem {
  id: number;
  productId: number;
  quantity: number;
  customization?: {
    type: string;
    details?: any;
    additionalPrice: number;
  };
  product: Product;
  subtotal: number;
}

export interface Cart {
  items: CartItem[];
  totalItems: number;
  subtotal: number;
  total: number;
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('authToken');
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Auth methods
  async login(email: string, password: string) {
    const data = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
    
    this.token = data.data.token;
    localStorage.setItem('authToken', this.token!);
    return data;
  }

  async getCurrentUser() {
    return this.request('/auth/me');
  }

  logout() {
    this.token = null;
    localStorage.removeItem('authToken');
  }

  // Product methods
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const queryString = queryParams.toString();
    return this.request(`/products${queryString ? `?${queryString}` : ''}`);
  }

  async getProduct(id: number) {
    return this.request(`/products/${id}`);
  }

  async getFeaturedProducts() {
    return this.request('/products/featured');
  }

  async getProductsByCategory(category: string) {
    return this.request(`/products/category/${category}`);
  }

  // Customization methods
  async getCustomizationOptions(productId: number) {
    return this.request(`/customization/${productId}`);
  }

  async calculateCustomizationPrice(productId: number, customizations: any[]) {
    return this.request(`/customization/${productId}/calculate`, {
      method: 'POST',
      body: JSON.stringify({ customizations }),
    });
  }

  // Cart methods
  async getCart() {
    return this.request('/cart');
  }

  async addToCart(productId: number, quantity: number = 1, customization?: any) {
    return this.request('/cart/add', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity, customization }),
    });
  }

  async removeFromCart(itemId: number) {
    return this.request(`/cart/item/${itemId}`, {
      method: 'DELETE',
    });
  }

  // Wishlist methods
  async getWishlist() {
    return this.request('/wishlist');
  }

  async addToWishlist(productId: number) {
    return this.request(`/wishlist/${productId}`, {
      method: 'POST',
    });
  }

  async removeFromWishlist(productId: number) {
    return this.request(`/wishlist/${productId}`, {
      method: 'DELETE',
    });
  }

  // Order methods
  async createOrder(orderData: any) {
    return this.request('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getOrders() {
    return this.request('/orders');
  }

  async getOrder(id: number) {
    return this.request(`/orders/${id}`);
  }
}

export const apiService = new ApiService();
